#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import struct
import json
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime

class GameSocketDecoder:
    def __init__(self):
        self.gameNames = {
            "射击飞艇": "Shooting Airship Game",
            "E5B084E587BBE9A39EE88987": "射击飞艇"
        }

    def hexToBytes(self, hexString: str) -> bytes:
        cleanHex = hexString.replace(' ', '').replace('\n', '').replace('\r', '')
        return bytes.fromhex(cleanHex)

    def readVarint(self, data: bytes, offset: int) -> Tuple[int, int]:
        result = 0
        shift = 0
        pos = offset
        while pos < len(data):
            byte = data[pos]
            pos += 1
            result |= (byte & 0x7F) << shift
            if (byte & 0x80) == 0:
                break
            shift += 7
            if shift >= 64:
                break
        return result, pos

    def readLengthDelimited(self, data: bytes, offset: int) -> Tuple[bytes, int]:
        length, newOffset = self.readVarint(data, offset)
        if newOffset + length > len(data):
            return b'', newOffset
        return data[newOffset:newOffset + length], newOffset + length

    def decodeStringField(self, data: bytes, offset: int) -> Tuple[str, int]:
        stringBytes, newOffset = self.readLengthDelimited(data, offset)
        try:
            return stringBytes.decode('utf-8'), newOffset
        except UnicodeDecodeError:
            return stringBytes.hex(), newOffset

    def decodeMessageFields(self, data: bytes) -> Dict[str, Any]:
        message = {}
        pos = 0
        while pos < len(data):
            tag, pos = self.readVarint(data, pos)
            fieldNumber = tag >> 3
            wireType = tag & 0x7
            if fieldNumber == 1 and wireType == 0:
                value, pos = self.readVarint(data, pos)
                message['messageId'] = value
            elif fieldNumber == 2 and wireType == 2:
                value, pos = self.decodeStringField(data, pos)
                message['gameName'] = value
                message['gameNameEnglish'] = self.gameNames.get(value, value)
            elif fieldNumber == 3 and wireType == 0:
                value, pos = self.readVarint(data, pos)
                message['amount'] = value
                message['amountFormatted'] = f"{value:,}"
            elif fieldNumber == 4 and wireType == 2:
                value, pos = self.decodeStringField(data, pos)
                message['username'] = value.strip()
            elif fieldNumber == 5 and wireType == 0:
                value, pos = self.readVarint(data, pos)
                message['timestamp'] = value
                if 1000000000 <= value <= 9999999999:
                    try:
                        dt = datetime.fromtimestamp(value)
                        message['datetime'] = dt.strftime('%Y-%m-%d %H:%M:%S')
                    except (ValueError, OSError):
                        pass
                elif value > 9999999999:
                    try:
                        dt = datetime.fromtimestamp(value / 1000)
                        message['datetime'] = dt.strftime('%Y-%m-%d %H:%M:%S')
                    except (ValueError, OSError):
                        pass
            else:
                if wireType == 0:
                    _, pos = self.readVarint(data, pos)
                elif wireType == 2:
                    _, pos = self.readLengthDelimited(data, pos)
                elif wireType == 1:
                    pos += 8
                elif wireType == 5:
                    pos += 4
                else:
                    break
        return message

    def decodeSocketMessage(self, hexData: str) -> Dict[str, Any]:
        data = self.hexToBytes(hexData)
        result = {
            'success': False,
            'totalLength': len(data),
            'messageType': None,
            'header': {},
            'messages': [],
            'rawHexPreview': hexData[:100] + "..." if len(hexData) > 100 else hexData
        }
        if len(data) < 10:
            result['error'] = 'Data too short'
            return result
        result['header'] = {
            'first4Bytes': data[:4].hex(),
            'lengthLE': struct.unpack('<I', data[:4])[0],
            'lengthBE': struct.unpack('>I', data[:4])[0]
        }
        marker = b'broadhallmessage'
        markerPos = data.find(marker)
        if markerPos == -1:
            result['error'] = 'broadhallmessage marker not found'
            return result
        result['messageType'] = 'broadhallmessage'
        result['markerPosition'] = markerPos
        pos = markerPos + len(marker)
        messageCount = 0
        while pos < len(data) and messageCount < 50:
            if pos >= len(data) or data[pos] != 0x0A:
                nextPos = data.find(b'\x0A', pos + 1)
                if nextPos == -1:
                    break
                pos = nextPos
            if data[pos] == 0x0A:
                pos += 1
                msgLength, pos = self.readVarint(data, pos)
                if pos + msgLength > len(data):
                    break
                msgData = data[pos:pos + msgLength]
                decodedMsg = self.decodeMessageFields(msgData)
                if decodedMsg:
                    decodedMsg['sequence'] = messageCount + 1
                    result['messages'].append(decodedMsg)
                    messageCount += 1
                pos += msgLength
            else:
                pos += 1
        result['success'] = True
        result['totalMessages'] = len(result['messages'])
        return result

    def formatOutput(self, decodedData: Dict[str, Any]) -> str:
        lines = []
        lines.append("=" * 70)
        lines.append("GAME SOCKET MESSAGE DECODER")
        lines.append("=" * 70)
        lines.append(f"Status: {'SUCCESS' if decodedData['success'] else 'FAILED'}")
        lines.append(f"Total Length: {decodedData['totalLength']} bytes")
        lines.append(f"Message Type: {decodedData.get('messageType', 'Unknown')}")
        lines.append(f"Total Messages: {decodedData.get('totalMessages', 0)}")
        if 'error' in decodedData:
            lines.append(f"Error: {decodedData['error']}")
        if 'header' in decodedData:
            lines.append(f"\nHeader Info:")
            lines.append(f"  First 4 bytes: {decodedData['header']['first4Bytes']}")
            lines.append(f"  Length (LE): {decodedData['header']['lengthLE']}")
            lines.append(f"  Length (BE): {decodedData['header']['lengthBE']}")
        if decodedData.get('messages'):
            lines.append("\n" + "=" * 70)
            lines.append("DECODED MESSAGES")
            lines.append("=" * 70)
            for msg in decodedData['messages']:
                lines.append(f"\nMessage #{msg.get('sequence', '?')}:")
                lines.append(f"  Message ID: {msg.get('messageId', 'N/A')}")
                lines.append(f"  Game: {msg.get('gameNameEnglish', msg.get('gameName', 'N/A'))}")
                lines.append(f"  Username: {msg.get('username', 'N/A')}")
                lines.append(f"  Amount: {msg.get('amountFormatted', msg.get('amount', 'N/A'))}")
                if 'datetime' in msg:
                    lines.append(f"  DateTime: {msg['datetime']}")
                else:
                    lines.append(f"  Timestamp: {msg.get('timestamp', 'N/A')}")
        return "\n".join(lines)

    def saveToJson(self, decodedData: Dict[str, Any], filename: str = 'decoded_socket_messages.json'):
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(decodedData, f, indent=2, ensure_ascii=False)

def main():
    hexData = input("Nhập hexData (hex string, cách nhau bởi khoảng trắng): ").strip()
    
    decoder = GameSocketDecoder()
    decodedData = decoder.decodeSocketMessage(hexData)
    
    print(decoder.formatOutput(decodedData))
    decoder.saveToJson(decodedData)
    print(f"\nDecoded data saved to 'decoded_socket_messages.json'")
    
    if decodedData['success']:
        print(f"\n✅ Successfully decoded {decodedData['totalMessages']} messages")
        print("📋 Message structure:")
        print("   - Message ID: Game notification identifier")
        print("   - Game Name: 射击飞艇 (Shooting Airship Game)")
        print("   - Username: Player who triggered the notification")
        print("   - Amount: Game-related value (score, bet, win amount)")
        print("   - Timestamp: When the event occurred")
    else:
        print(f"\n❌ Failed to decode: {decodedData.get('error', 'Unknown error')}")


if __name__ == "__main__":
    main()
